<?php

namespace App\Http\Controllers;

use App\Models\StaffLedger;
use App\Models\Sale;
use App\Models\Invoice;
use App\Models\Purchase;
use Illuminate\Http\Request;
use Carbon\Carbon;

class TrialBalanceController extends Controller
{
    public function generateTrialBalance(Request $request)
    {
        $request->validate([
            'from_date' => 'required|date',
            'to_date' => 'required|date|after_or_equal:from_date',
        ]);

        $fromDate = Carbon::parse($request->from_date)->startOfDay();
        $toDate = Carbon::parse($request->to_date)->endOfDay();

        try {
            $trialBalanceData = [];

            // 1. Cash in Hand
            $cashBalance = $this->getCashInHandBalance($fromDate, $toDate);
            $trialBalanceData[] = [
                'account_name' => 'Cash in Hand',
                'debit' => $cashBalance > 0 ? $cashBalance : 0,
                'credit' => $cashBalance < 0 ? abs($cashBalance) : 0,
            ];

            // 2. Sales Revenue
            $salesRevenue = $this->getTotalSalesRevenue($fromDate, $toDate);
            $trialBalanceData[] = [
                'account_name' => 'Sales',
                'debit' => 0,
                'credit' => $salesRevenue,
            ];

            // 3. Purchase
            $purchaseAmount = $this->getTotalPurchaseAmount($fromDate, $toDate);
            $trialBalanceData[] = [
                'account_name' => 'Purchase',
                'debit' => $purchaseAmount,
                'credit' => 0,
            ];

            // 4. Ledger Accounts
            $ledgerBalances = $this->getLedgerBalances($fromDate, $toDate);
            foreach ($ledgerBalances as $ledger) {
                $trialBalanceData[] = [
                    'account_name' => $ledger['name'],
                    'debit' => $ledger['balance'] > 0 ? $ledger['balance'] : 0,
                    'credit' => $ledger['balance'] < 0 ? abs($ledger['balance']) : 0,
                ];
            }

            // Calculate totals
            $totalDebit = array_sum(array_column($trialBalanceData, 'debit'));
            $totalCredit = array_sum(array_column($trialBalanceData, 'credit'));

            return response()->json([
                'success' => true,
                'data' => [
                    'accounts' => $trialBalanceData,
                    'totals' => [
                        'total_debit' => $totalDebit,
                        'total_credit' => $totalCredit,
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error generating trial balance: ' . $e->getMessage(),
            ], 500);
        }
    }

    private function getCashInHandBalance($fromDate, $toDate)
    {
        // Get closing balance from CashInHandStatementController
        $request = new Request([
            'from_date' => $fromDate->format('Y-m-d'),
            'to_date' => $toDate->format('Y-m-d'),
        ]);

        $response = app(\App\Http\Controllers\CashInHandStatementController::class)->getStatement($request);
        $responseData = json_decode($response->getContent(), true);

        return $responseData['balance'] ?? 0;
    }

    private function getTotalSalesRevenue($fromDate, $toDate)
    {
        // Total sales revenue from POS sales table
        $salesRevenue = Sale::whereBetween('created_at', [$fromDate, $toDate])
            ->sum('total') ?? 0;

        // Total invoice revenue
        $invoiceRevenue = Invoice::whereBetween('invoice_date', [$fromDate, $toDate])
            ->whereNull('deleted_by')
            ->whereNull('deleted_at')
            ->sum('total_amount') ?? 0;

        return $salesRevenue + $invoiceRevenue;
    }

    private function getTotalPurchaseAmount($fromDate, $toDate)
    {
        // Total purchase amount
        $purchaseAmount = Purchase::whereBetween('date_of_purchase', [$fromDate, $toDate])
            ->sum('total') ?? 0;

        return $purchaseAmount;
    }

    private function getLedgerBalances($fromDate, $toDate)
    {
        // Get all ledgers excluding Cash in Hand group
        $ledgers = StaffLedger::where('account_group', '!=', 'Cash in Hand')->get();
        $ledgerBalances = [];

        foreach ($ledgers as $ledger) {
            // Get statement balance for each ledger
            $balance = $this->getLedgerStatementBalance($ledger, $fromDate, $toDate);
            
            if ($balance != 0) { // Only include ledgers with non-zero balance
                $ledgerBalances[] = [
                    'name' => $ledger->name,
                    'balance' => $balance,
                ];
            }
        }

        return $ledgerBalances;
    }

    private function getLedgerStatementBalance($ledger, $fromDate, $toDate)
    {
        try {
            // Use StatementController to get ledger balance
            // StatementController expects staff_id for 'Other' type
            $request = new Request([
                'person_type' => 'Other',
                'person_id' => $ledger->staff_id,
                'from_date' => $fromDate->format('Y-m-d'),
                'to_date' => $toDate->format('Y-m-d'),
            ]);

            $response = app(\App\Http\Controllers\StatementController::class)->generateStatement($request);
            $responseData = json_decode($response->getContent(), true);

            if ($responseData['success'] ?? false) {
                $transactions = $responseData['transactions'] ?? [];
                $openingBalance = $responseData['opening_balance'] ?? 0;

                // Calculate final balance
                $finalBalance = $openingBalance;
                foreach ($transactions as $transaction) {
                    $finalBalance = $transaction['running_balance'] ?? $finalBalance;
                }

                return $finalBalance;
            }

            return $ledger->opening_balance ?? 0;
        } catch (\Exception $e) {
            // Fallback to opening balance if statement generation fails
            return $ledger->opening_balance ?? 0;
        }
    }
}
